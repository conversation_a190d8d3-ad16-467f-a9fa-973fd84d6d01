import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DependentInformations } from '../../models';
import { PlanSummary } from 'src/app/pages/settings/pages/plan/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { AddSchedule, ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService, StudentPlanService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { PlanAssignedSuccessComponent } from '../plan-assigned-success/plan-assigned-success.component';
import { CustomerOrdersRes, PaymentForProduct, ProductStatus, TransactionTypes } from 'src/app/pages/shop/models';
import { StoreProductService } from 'src/app/pages/shop/services';
import { PaymentMethodsComponent } from 'src/app/shared/components/payment-methods/payment-methods.component';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { CardDetailsResponse } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { AuthService } from 'src/app/auth/services';
import { Account } from 'src/app/auth/models/user.model';
import moment from 'moment';
import { AppToasterService } from 'src/app/shared/services';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { DependentService } from 'src/app/pages/profile/services';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CBGetResponse } from 'src/app/shared/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { PdfViewerComponent } from 'src/app/pages/user-document/pages/pdf-viewer/pdf-viewer.component';
import { SignedDocumentsInfo } from 'src/app/pages/user-document/models';
import { MatRadioModule } from '@angular/material/radio';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { SignedDocumentService } from 'src/app/pages/user-document/services';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    SharedModule,
    CommonModule,
    MatIconModule,
    MatSidenavModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatRadioModule,
    ReactiveFormsModule,
    MatDatepickerModule
  ],
  COMPONENTS: [PlanAssignedSuccessComponent, PaymentMethodsComponent, PdfViewerComponent]
};

@Component({
  selector: 'app-continue-to-checkout',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './continue-to-checkout.component.html',
  styleUrls: ['./continue-to-checkout.component.scss', '../assign-plan-and-product/assign-plan-and-product.component.scss']
})
export class ContinueToCheckoutComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() selectedStudentPlan!: PlanSummary | undefined;
  @Input() selectedInstrumentName!: string;
  @Input() bookPlanFormValue!: AddSchedule;
  @Input() shoppingCart!: Array<CustomerOrdersRes>;
  @Input() cartId!: number;
  @Input() isPlanRenewal!: boolean;

  cardDetails!: CardDetailsResponse | null;
  isPlanAssignedSideNavOpen = false;
  isPaymentDone = false;
  isRePayment = false;
  productStatus = ProductStatus;
  accManagerDetails!: Account | null;
  discountAmount = 0;
  productFixedDiscount = 0;
  showDiscountField = false;
  showPlanPriceInput = false;
  discountError: string | null = null;
  isRecurringDiscount = false;
  isAddressIncomplete = false;
  isPDFViewerSideNavOpen = false;
  showPaymentTemplate = false;
  isClientRequest = true;
  currentMonthPayment = 0;
  maxDate = new Date();
  updatedPlanId!: number;
  documentInfo!: SignedDocumentsInfo;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() closeAllSideNav = new EventEmitter<void>();

  constructor(
    protected readonly planSummaryService: PlanSummaryService,
    private readonly datePipe: DatePipe,
    private readonly schedulerService: SchedulerService,
    private readonly authService: AuthService,
    private readonly storeProductService: StoreProductService,
    private readonly studentPlanService: StudentPlanService,
    private readonly cdr: ChangeDetectorRef,
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly dependentService: DependentService,
    private readonly signedDocumentService: SignedDocumentService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['shoppingCart']?.currentValue) {
      this.shoppingCart = changes['shoppingCart'].currentValue;
      this.calculateTotalPrice(false);
      this.getCardDetailsForPaymentInitiate();
    }
    if (changes['bookPlanFormValue']?.currentValue) {
      this.bookPlanFormValue = changes['bookPlanFormValue'].currentValue;
      this.getCurrentMonthPayment();
    }
    this.getAccountManagerDetails();
  }

  getAccountManagerDetails(): void {
    this.showPageLoader = true;
    this.authService
      .getUserDetailsFromId(this.selectedStudentDetails?.accountManagerId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.accManagerDetails = res.result;
          if(this.accManagerDetails?.productDiscountPercentage && this.shoppingCart?.length) {
            this.productFixedDiscount = (this.getActualPrice(false)) * (this.accManagerDetails?.productDiscountPercentage / 100);
          }
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentMonthPayment(): void {
    if (this.isPlanRenewal) {
      return;
    }
    this.showPageLoader = true;
    this.paymentService
      .add(
        {
          startDate: this.datePipe.transform(this.getScheduleDate, this.constants.dateFormats.yyyy_MM_dd) ?? '',
          daysOfSchedule: this.bookPlanFormValue.daysOfSchedule ?? [],
          paidAmount: this.selectedStudentPlan?.planPrice
        },
        API_URL.payment.currentMonthPaymentCalculationForRecurringPlan
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<number>) => {
          this.currentMonthPayment = res?.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCardDetailsForPaymentInitiate(): void {
    this.paymentService.userCardDetails$.pipe(takeUntil(this.destroy$)).subscribe(card => {
      if (card) {
        this.cardDetails = card;
        this.onProductsPayment();
        this.cdr.detectChanges();
      }
    });
  }

  getActualPrice(isDiscountOnPlan: boolean): number {
    if (isDiscountOnPlan) {
      return this.selectedStudentPlan ? this.selectedStudentPlan?.planPrice : 0;
    }
    return this.shoppingCart
      ? this.shoppingCart
          .filter(p => p.status !== ProductStatus.OUT_OF_STOCK)
          .reduce((sum, item) => {
            return sum + (item.totalPrice || item.productPrice * item.quantity);
          }, 0)
      : 0;
  }

  capDiscount(event: Event, isDiscountOnPlan: boolean): void {
    const maxPrice = this.selectedStudentPlan && isDiscountOnPlan ? this.currentMonthPayment + this.selectedStudentPlan?.serviceFees + this.selectedStudentPlan?.registrationFees : this.getActualPrice(false) - this.productFixedDiscount;
    if (this.discountAmount > maxPrice) {
      this.discountAmount = maxPrice;
      if (event) {
        (event.target as HTMLInputElement).value = maxPrice.toString();
      }
    }
  }

  calculateTotalPrice(isDiscountOnPlan: boolean): number {
    return this.getActualPrice(isDiscountOnPlan) - (+this.discountAmount + this.productFixedDiscount);
  }

  get totalFirstMonthPayment(): number {
    if (!this.selectedStudentPlan) {
      return 0;
    }
    return (
      this.currentMonthPayment + this.selectedStudentPlan?.serviceFees + this.selectedStudentPlan?.registrationFees - this.discountAmount
    );
  }

  get getScheduleDate(): string {
    return this.isPlanRenewal
      ? moment(this.bookPlanFormValue.scheduleDate).add(1, 'day').format(this.constants.dateFormats.yyyy_MM_DD)
      : this.bookPlanFormValue.scheduleDate ?? '';
  }

  clearDiscount(isDiscountOnPlan: boolean): void {
    this.showDiscountField = false;
    this.discountAmount = 0;
    this.discountError = null;
    this.isRecurringDiscount = false;
    this.calculateTotalPrice(isDiscountOnPlan);
  }

  closeBookAssignedPlan(): void {
    this.closeSideNav.emit();
  }

  closeAll(): void {
    this.isPlanAssignedSideNavOpen = false;
    this.isPDFViewerSideNavOpen = false;
    this.closeAllSideNav.emit();
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
      this.isAddressIncomplete = isIncomplete;
      this.cdr.markForCheck();
    });
  }

  getPaymentParams(): PaymentForProduct {
    return {
      cartId: this.cartId,
      paidAmount: this.calculateTotalPrice(false),
      totalAmount: this.getActualPrice(false),
      discountAmount: +this.discountAmount + this.productFixedDiscount,
      transactionType: this.cardDetails?.transactionType ?? TransactionTypes.CARD,
      paidDate: new Date(),
      locationId: this.selectedStudentDetails?.locationId,
      dependentInformationId: this.selectedStudentDetails?.id,
      customerVaultId: this.cardDetails?.customerVaultId,
      chequeNumber: this.cardDetails?.chequeNumber
    };
  }

  onProductsPayment(): void {
    if (!this.cartId) {
      return;
    }
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
      this.toasterService.error(this.constants.errorMessages.addressIncomplete);
      return;
    }
    this.storeProductService
      .add(this.getPaymentParams(), API_URL.storeProduct.paymentForProducts)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.isPaymentDone = true;
          this.isPlanAssignedSideNavOpen = true;
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.cdr.detectChanges();
        },
        error: err => {
          this.isPaymentDone = false;
          this.isRePayment = true;
          this.toasterService.error(this.constants.errorMessages.paymentFailed);
          this.cdr.detectChanges();
        }
      });
  }

  openAssignedPlan(): void {
    if (this.isPaymentDone) {
      this.isPlanAssignedSideNavOpen = true;
    }
  }

  onAssignStudentPlan(): void {
    this.showBtnLoader = true;
    const plan = this.selectedStudentPlan as PlanSummary;
    this.dependentService
      .add(
        {
          planSummaryId: plan.id,
          dependentInformationId: this.selectedStudentDetails?.id,
          instrumentDetailId: plan.isEnsembleAvailable ? null : plan.instrumentId,
          isEnsembleAvailable: plan.isEnsembleAvailable
        },
        API_URL.dependentInformations.assignStudentPlan
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<{ studentPlanId: number }>) => {
          this.selectedStudentPlan = { ...plan, id: res.result.studentPlanId };
          this.assignStudentDocument();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  assignStudentDocument(): void {
    this.signedDocumentService
      .add(
        {},
        `${API_URL.signedDocuments.assignAgreementDocument}?studentId=${this.selectedStudentDetails?.id}&planId=${this.selectedStudentPlan?.id}&isClientRequest=${this.isClientRequest}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateStudentPlan();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  updateStudentPlan(): void {
    this.showBtnLoader = true;
    this.dependentService
      .update(
        {
          startDate: DateUtils.getUtcRangeForLocalDate(this.getScheduleDate).startUtc,
          studentPlanId: this.isPlanRenewal ? this.updatedPlanId : this.selectedStudentPlan?.id,
          planAmount: this.isRecurringDiscount ? this.calculateTotalPrice(true) : this.selectedStudentPlan?.planPrice,
          discountedAmount: +this.discountAmount,
          totalAmount: this.selectedStudentPlan?.planPrice,
          registrationFees: this.isPlanRenewal ? 0 : this.selectedStudentPlan?.registrationFees,
          serviceFees: this.isPlanRenewal ? 0 : this.selectedStudentPlan?.serviceFees,
          isRecurringDiscount: this.isRecurringDiscount,
          firstPayment: this.currentMonthPayment,
          serialNumber: this.bookPlanFormValue.serialNumber,
          isPlanRenewal: this.isPlanRenewal
        },
        API_URL.dependentInformations.updateStudentPlan
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          this.documentInfo = {
            ...this.selectedStudentPlan,
            ...this.bookPlanFormValue,
            ...res.result,
            id: res.result.agreementDocumentId,
            planId: this.isPlanRenewal ? this.updatedPlanId : this.selectedStudentPlan?.id,
            planDetails: this.selectedStudentPlan?.plandetails?.items ?? [],
            planInstrument: this.selectedInstrumentName,
            planStartDate: this.getScheduleDate,
            firstPayment: this.currentMonthPayment,
            planAmount: this.selectedStudentPlan?.planPrice ?? 0,
            discountedAmount: +this.discountAmount,
            accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0,
            fullFilePath: res.result.fullFilePath,
            recurringScheduleId: res.result.id ?? 0,
            classType: this.selectedStudentPlan?.isEnsembleAvailable ? ClassTypes.ENSEMBLE_CLASS : ClassTypes.RECURRING
          };
          this.selectedStudentPlan = {
            ...this.selectedStudentPlan,
            discountedAmount: +this.discountAmount,
            isRecurringDiscount: this.isRecurringDiscount,
            plan: this.selectedStudentPlan?.plan ?? 0
          } as PlanSummary;
          if (this.isClientRequest) {
            this.isPlanAssignedSideNavOpen = true;
          } else {
            this.isPDFViewerSideNavOpen = true;
          }
          this.isPlanRenewal
            ? this.toasterService.success(this.constants.successMessages.renewedSuccessfully.replace('{item}', 'Plan'))
            : this.toasterService.success(this.constants.successMessages.assignedSuccessfully.replace('{item}', 'Plan'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onSubmit(): void {
    if (this.isPlanRenewal || this.selectedStudentPlan?.isRentalPlan) {
      return;
    }
    this.showBtnLoader = true;
    this.schedulerService
      .add(
        {
          ...this.bookPlanFormValue,
          scheduleDate: DateUtils.getUtcRangeForLocalDate(this.bookPlanFormValue.scheduleDate ?? '').startUtc,
          scheduleStartTime: DateUtils.toUTC(this.bookPlanFormValue.scheduleStartTime, 'yyyy-MM-DDTHH:mm:ss'),
          scheduleEndTime: DateUtils.toUTC(this.bookPlanFormValue.scheduleEndTime, 'yyyy-MM-DDTHH:mm:ss'),
          isDraftSchedule: true
        },
        API_URL.crud.create
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<SignedDocumentsInfo>) => {
          this.documentInfo = {
            ...this.selectedStudentPlan,
            ...this.bookPlanFormValue,
            ...res.result,
            id: res.result.agreementDocumentId,
            planDetails: this.selectedStudentPlan?.plandetails?.items ?? [],
            planInstrument: this.selectedInstrumentName,
            planStartDate: this.getScheduleDate,
            firstPayment: this.currentMonthPayment,
            planAmount: this.selectedStudentPlan?.planPrice ?? 0,
            discountedAmount: +this.discountAmount,
            accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0,
            fullFilePath: res.result.fullFilePath,
            recurringScheduleId: res.result.id ?? 0
          };
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        complete: () => {
          this.updateStudentPlan();
        }
      });
  }

  onRenewPlan(): void {
    if (!this.isPlanRenewal) {
      return;
    }
    this.showBtnLoader = true;
    this.studentPlanService
      .add({}, `${API_URL.studentPlans.renewExistingPlan}?studentPlanId=${this.selectedStudentPlan?.id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.updatedPlanId = res.result.updatedPlanId;
          this.documentInfo = {
            ...this.selectedStudentPlan,
            ...this.bookPlanFormValue,
            ...res.result,
            id: res.result.agreementDocumentId,
            planId: this.updatedPlanId,
            planDetails: this.selectedStudentPlan?.plandetails?.items ?? [],
            planInstrument: this.selectedInstrumentName,
            planStartDate: this.getScheduleDate,
            firstPayment: this.currentMonthPayment,
            planAmount: this.selectedStudentPlan?.planPrice ?? 0,
            discountedAmount: +this.discountAmount,
            accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0,
            fullFilePath: res.result.fullFilePath,
            recurringScheduleId: res.result.id ?? 0
          };
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        complete: () => {
          this.updateStudentPlan();
        }
      });
  }
}
