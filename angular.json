{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"octopus": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/angular-base-project-v3", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest", "src/manifest.json", "src/firebase-messaging-sw.js"], "styles": ["src/styles.scss", "node_modules/bootstrap/scss/bootstrap.scss", {"input": "src/bootstrap4-light-blue.scss", "bundleName": "bootstrap4-light-blue", "inject": false}, {"input": "src/bootstrap4-dark-blue.scss", "bundleName": "bootstrap4-dark-blue", "inject": false}], "scripts": [], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "6kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw-prod.js"}, {"replace": "src/manifest.json", "with": "src/manifest-prod.json"}]}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw-dev.js"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "serviceWorker": true, "outputHashing": "all"}, "stage": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw-stage.js"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "all"}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}, {"replace": "src/firebase-messaging-sw.js", "with": "src/firebase-messaging-sw-qa.js"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "all"}, "local": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "octopus:build:production"}, "development": {"buildTarget": "octopus:build:development"}, "stage": {"buildTarget": "octopus:build:stage"}, "qa": {"buildTarget": "octopus:build:qa"}, "local": {"buildTarget": "octopus:build:local"}}, "defaultConfiguration": "local"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "octopus:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.scss", "node_modules/bootstrap/scss/bootstrap.scss"], "scripts": []}}}}}, "cli": {"analytics": "3a73aff0-73f5-409b-9a0a-585284ad42c1"}}