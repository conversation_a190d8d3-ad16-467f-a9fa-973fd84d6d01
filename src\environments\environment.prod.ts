import { versions } from './versions';

export const environment = {
  versions,
  production: true,
  hostUrl: '',
  apiUrl: 'https://qa.baseprojectjava.thesunflowerlab.com/api',
  frontendUrl: 'https://prod.octopusmusicschool.com/',
  signalRUrl: 'https://prod-api.octopusmusicschool.com/signalr-octopus-chat',
  paymentTokenizationKey: 'd6FRy3-dJxjyu-hU3JT7-4f66eT',
  persistUserSession: true,
  sentryKey: '', // sentry key not required for local environments
  firebase: {
    apiKey: '',
    authDomain: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
    measurementId: ''
  },
  swUpdate: true,
  swUpdateFooter: true,
  encryptionService: {
    enable: false,
    secretKey: 'BfBRoYxsRKGnx3DlPgnsmei7qVHTD62o',
    secretIV: 'cGsgbxxqbcYN6HZG'
  },
  compressAfterFileSize: 2
};
