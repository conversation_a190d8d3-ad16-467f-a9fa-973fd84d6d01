@import "src/assets/scss/theme/_mixins.scss";
@import "src/assets/scss/variables";

.auth-page-with-header {
  height: calc(100vh - 104px) !important;

  .header-tab-with-btn {
    min-height: 60px !important;

    .page-title {
      color: $black-color;
      font-weight: 600;
    }

    .mobile-view {
      display: none;
    }
  }

  .schedule-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .o-card {
    display: flex;
    padding: 15px !important;
    margin-bottom: 20px;
    font-weight: 600;

    .o-card-title {
      @include flex-content-space-between;
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 3px;
    }

    .schedule-detail {
      @include flex-content-space-between;
      width: 100%;

      .student-details {
        .icons img {
          filter: $gray-filter;
          cursor: pointer;
          height: 30px;
          width: 28px;
        }

        .attendance-status {
          @include flex-content-center;
          width: fit-content;
          background-color: $light-yellow-color;
          color: $yellow-color;
          font-size: 14px;
          height: 33px;
          padding: 7px 10px;
          border-radius: 4px;
          font-weight: 400;

          img {
            height: 16px;
            width: 16px;
            margin-right: 5px;
          }

          .pending-img {
            filter: $yellow-filter;
          }
        }

        .present-status {
          background-color: $header-schedule-bg-color;
          color: $primary-color;
        }

        .absent-status {
          background-color: $red-bg-color;
          color: $red-btn;
        }

        .present-img {
          filter: $primary-color-filter !important;
        }

        .no-show-img {
          filter: $red-filter !important;
        }

        .absent:hover {
          filter: $red-filter !important;
        }

        .present:hover {
          filter: $primary-color-filter !important;
        }
      }
    }

    .o-card-body {
      @include flex-content-align-center;

      img {
        filter: $primary-color-filter;
        height: 16px;
        width: 16px;
        margin-right: 0.25rem;
      }
    }

    .schedule-content {
      @include flex-content-align-center;
      font-size: 14px;
      flex-wrap: wrap;

      mat-icon {
        height: inherit !important;
        font-size: 15px !important;
        width: 12px !important;
        color: $primary-color !important;
      }
    }

    .student-detail-content {
      display: flex;
      justify-content: flex-end;
    }

    .class-lesson-type {
      font-size: 14px;
      @include flex-content-align-center;

      .dot {
        display: inline-block;
        margin: 0px 5px;
      }
    }

    .placeholder-name {
      height: 45px;
      width: 45px;
    }

    .arrival-time {
      @include flex-content-center;
      color: $yellow-color;
      border-radius: 4px;
      height: 33px;
      width: 100px;
      background-color: $light-yellow-color;
      font-size: 13px;
      padding: 0px 3px;

      img {
        height: 13px;
        width: 13px;
        margin-right: 3px;
        filter: $yellow-filter;
      }
    }
  }
}

::ng-deep {
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-raised-button > .mat-icon {
    @include flex-content-center;
    z-index: 1;
    filter: $white-filter;
    margin-right: 0px !important;
  }
}

.remaining-instrument-available-count {
  cursor: pointer;
  color: $black-color;
  font-weight: 800;
  font-size: 14px;
}

@media (max-width: 767px) {
  .auth-page-with-header {
    .header-tab-with-btn {
      .action-btn-wrapper {
        display: flex;
      }

      .action-btn-wrapper {
        display: none;
      }

      .mobile-view {
        display: block;
      }
    }

    .o-card {
      .student-detail-content {
        justify-content: flex-start !important;
      }

      .o-card-body,
      .schedule-detail {
        flex-direction: column;
        align-items: start;

        .dot {
          display: none;
        }
      }
    }
  }
}
